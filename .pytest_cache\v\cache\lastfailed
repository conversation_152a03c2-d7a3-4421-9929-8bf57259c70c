{"tests/test_search_tool.py::TestSearchTool::test_search_empty_query_handling": true, "tests/test_config.py::TestMCPConfig::test_add_server_config": true, "tests/test_config.py::TestMCPConfig::test_remove_server_config": true, "tests/test_config.py::TestMCPConfig::test_config_validation": true, "tests/test_buyback_data_tool.py::TestBuybackDataTool": true, "tests/test_esop_tool.py::TestESOPTool": true, "tests/test_file_read_tool.py::TestFileReadTool": true, "tests/test_integration.py::TestMCPIntegration": true, "tests/test_notice_data_tool.py::TestNoticeDataTool": true, "tests/test_search_tool.py::TestSearchTool": true, "tests/test_stock_data_search_tool.py::TestStockDataSearchTool": true}