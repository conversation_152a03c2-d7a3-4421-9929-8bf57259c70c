#!/usr/bin/env python3
"""
Simple verification script for the JSON configuration system.
"""

import sys
import traceback

def main():
    try:
        print("🔍 Verifying JSON Configuration System...")
        
        # Test imports
        print("📦 Testing imports...")
        from mcp_client import MCPConfig, JSONConfigManager, ServerConfig
        print("✅ All imports successful")
        
        # Test JSON manager
        print("\n🗂️ Testing JSONConfigManager...")
        manager = JSONConfigManager("mcp_servers.json")
        servers = manager.list_servers()
        print(f"✅ Found {len(servers)} servers: {', '.join(servers)}")
        
        # Test each server config
        for server_name in servers:
            config = manager.get_server_config(server_name)
            tools = manager.get_server_tools(server_name)
            print(f"✅ {server_name}: {config.url} ({len(tools)} tools)")
        
        # Test MCPConfig integration
        print("\n⚙️ Testing MCPConfig integration...")
        mcp_config = MCPConfig()
        mcp_servers = mcp_config.list_servers()
        print(f"✅ MCPConfig loaded {len(mcp_servers)} servers: {', '.join(mcp_servers)}")
        
        # Verify they match
        if set(servers) == set(mcp_servers):
            print("✅ JSONConfigManager and MCPConfig are in sync")
        else:
            print("❌ JSONConfigManager and MCPConfig are out of sync")
            return 1
        
        # Test adding a server
        print("\n➕ Testing server addition...")
        test_server = ServerConfig(
            name="test_verification",
            url="https://test.example.com/mcp",
            description="Test server for verification",
            demo_input="test",
            timeout=30.0,
            max_retries=3
        )
        
        mcp_config.add_server(test_server, tools=["test_tool"])
        
        # Verify it was added
        updated_servers = mcp_config.list_servers()
        if "test_verification" in updated_servers:
            print("✅ Server addition successful")
            
            # Clean up - remove the test server
            mcp_config.remove_server("test_verification")
            final_servers = mcp_config.list_servers()
            if "test_verification" not in final_servers:
                print("✅ Server removal successful")
            else:
                print("❌ Server removal failed")
                return 1
        else:
            print("❌ Server addition failed")
            return 1
        
        print("\n🎉 All verification tests passed!")
        print("The JSON configuration system is working correctly.")
        return 0
        
    except Exception as e:
        print(f"\n💥 Verification failed with error: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
