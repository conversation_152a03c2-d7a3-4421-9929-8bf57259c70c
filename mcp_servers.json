{"servers": {"cubemcp": {"name": "cubemcp", "url": "https://mcp.cubeflow.cn/api/mcp/", "description": "集成多种工具和提示词的mcp server", "demo_input": "今天北京的天气", "timeout": 30.0, "max_retries": 3, "tools": ["esop", "notice_data", "buyback_data", "crawl_tool", "file_read_tool", "search_tool", "stock_data_search"]}, "code_interpreter": {"name": "code_interpreter", "url": "https://mcp.cubeflow.cn/api/code_interpreter", "description": "Code interpreter MCP server for executing code and analysis", "demo_input": "print('Hello, <PERSON>!')", "timeout": 45.0, "max_retries": 3, "tools": ["execute_code", "analyze_code", "format_code"]}}, "metadata": {"version": "1.0.0", "last_updated": "2025-08-04T16:33:24.747001+00:00", "description": "MCP Server Configuration File - manages available MCP servers and their metadata"}}