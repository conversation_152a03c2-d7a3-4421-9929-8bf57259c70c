"""
Custom exceptions for the MCP client.

This module defines specific exception types for different error conditions
that can occur when working with MCP servers.
"""

from typing import Optional


class MCPClientError(Exception):
    """Base exception for all MCP client errors."""
    
    def __init__(self, message: str, server_name: Optional[str] = None) -> None:
        """
        Initialize the MCP client error.
        
        Args:
            message: Error message describing what went wrong
            server_name: Optional name of the server where the error occurred
        """
        super().__init__(message)
        self.server_name = server_name
        self.message = message
    
    def __str__(self) -> str:
        if self.server_name:
            return f"[{self.server_name}] {self.message}"
        return self.message


class MCPConnectionError(MCPClientError):
    """Raised when connection to an MCP server fails."""
    
    def __init__(
        self, 
        message: str, 
        server_name: Optional[str] = None,
        server_url: Optional[str] = None
    ) -> None:
        """
        Initialize the connection error.
        
        Args:
            message: Error message describing the connection failure
            server_name: Optional name of the server
            server_url: Optional URL of the server
        """
        super().__init__(message, server_name)
        self.server_url = server_url


class MCPAuthenticationError(MCPClientError):
    """Raised when authentication with an MCP server fails."""
    
    def __init__(
        self, 
        message: str, 
        server_name: Optional[str] = None,
        auth_challenge: Optional[str] = None
    ) -> None:
        """
        Initialize the authentication error.
        
        Args:
            message: Error message describing the authentication failure
            server_name: Optional name of the server
            auth_challenge: Optional WWW-Authenticate header value
        """
        super().__init__(message, server_name)
        self.auth_challenge = auth_challenge


class MCPServerError(MCPClientError):
    """Raised when an MCP server returns an error response."""
    
    def __init__(
        self, 
        message: str, 
        server_name: Optional[str] = None,
        status_code: Optional[int] = None,
        error_code: Optional[str] = None
    ) -> None:
        """
        Initialize the server error.
        
        Args:
            message: Error message from the server
            server_name: Optional name of the server
            status_code: Optional HTTP status code
            error_code: Optional MCP error code
        """
        super().__init__(message, server_name)
        self.status_code = status_code
        self.error_code = error_code


class MCPConfigurationError(MCPClientError):
    """Raised when there's an error in the MCP client configuration."""
    
    def __init__(self, message: str, config_key: Optional[str] = None) -> None:
        """
        Initialize the configuration error.
        
        Args:
            message: Error message describing the configuration issue
            config_key: Optional configuration key that caused the error
        """
        super().__init__(message)
        self.config_key = config_key


class MCPToolError(MCPClientError):
    """Raised when a tool call fails."""
    
    def __init__(
        self, 
        message: str, 
        server_name: Optional[str] = None,
        tool_name: Optional[str] = None,
        arguments: Optional[dict] = None
    ) -> None:
        """
        Initialize the tool error.
        
        Args:
            message: Error message describing the tool failure
            server_name: Optional name of the server
            tool_name: Optional name of the tool that failed
            arguments: Optional arguments passed to the tool
        """
        super().__init__(message, server_name)
        self.tool_name = tool_name
        self.arguments = arguments


class MCPResourceError(MCPClientError):
    """Raised when a resource operation fails."""
    
    def __init__(
        self, 
        message: str, 
        server_name: Optional[str] = None,
        resource_uri: Optional[str] = None
    ) -> None:
        """
        Initialize the resource error.
        
        Args:
            message: Error message describing the resource failure
            server_name: Optional name of the server
            resource_uri: Optional URI of the resource that failed
        """
        super().__init__(message, server_name)
        self.resource_uri = resource_uri
