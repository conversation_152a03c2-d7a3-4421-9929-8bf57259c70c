"""
MCP Client implementation.

This module provides the core MCP client functionality for connecting to
and interacting with MCP servers using Streamable HTTP transport.
"""

import logging
from contextlib import AsyncExitStack
from typing import Any, Dict, List, Optional

import httpx
from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client
from mcp.types import Prompt, Resource, Tool

from .config import AuthConfig, ServerConfig
from .exceptions import (
    MCPAuthenticationError,
    MCPClientError,
    MCPConnectionError,
    MCPResourceError,
    MCPServerError,
    MCPToolError,
)

logger = logging.getLogger(__name__)


class MinimalMCPClient:
    """
    A minimal MCP client that can connect to MCP servers using Streamable HTTP transport.

    This client demonstrates the core functionality needed to:
    - Connect to MCP servers via HTTP
    - Discover available tools, resources, and prompts
    - Invoke tools with parameters
    - Handle server responses
    """

    def __init__(self, auth_config: Optional[AuthConfig] = None) -> None:
        """
        Initialize the MCP client.

        Args:
            auth_config: Authentication configuration. If None, no authentication is used.
        """
        self.auth_config = auth_config or AuthConfig()
        self.exit_stack = AsyncExitStack()
        self.connected_servers: Dict[str, Dict[str, Any]] = {}

    async def connect_to_server(
        self, server_config: ServerConfig, test_connection: bool = True
    ) -> bool:
        """
        Connect to an MCP server using Streamable HTTP transport.

        Args:
            server_config: Configuration for the server to connect to
            test_connection: Whether to test the connection before establishing it

        Returns:
            True if connection successful, False otherwise

        Raises:
            MCPConnectionError: If connection fails
            MCPAuthenticationError: If authentication fails
        """
        server_name = server_config.name
        server_url = server_config.url

        try:
            logger.info(f"Connecting to MCP server '{server_name}' at {server_url}")

            # Prepare headers for authentication and MCP compatibility
            headers = self._get_request_headers()

            if test_connection:
                await self._test_server_connection(server_config, headers)

            # Establish the actual MCP connection
            read_stream, write_stream, _ = await self.exit_stack.enter_async_context(
                streamablehttp_client(server_url, headers=headers)
            )

            # Create client session
            session = await self.exit_stack.enter_async_context(
                ClientSession(read_stream, write_stream)
            )

            # Initialize the connection
            await session.initialize()

            # Store connection info
            self.connected_servers[server_name] = {
                "url": server_url,
                "session": session,
                "config": server_config,
                "tools": [],
                "resources": [],
                "prompts": [],
            }

            logger.info(f"Successfully connected to '{server_name}'")
            return True

        except MCPAuthenticationError:
            raise
        except MCPConnectionError:
            raise
        except Exception as e:
            logger.error(f"Failed to connect to '{server_name}': {e}")
            raise MCPConnectionError(
                f"Failed to connect to server: {e}",
                server_name=server_name,
                server_url=server_url,
            ) from e

    def _get_request_headers(self) -> Dict[str, str]:
        """
        Get HTTP headers for requests.

        Returns:
            Dictionary of headers to include in requests
        """
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/event-stream",
        }

        # Add authentication headers
        auth_headers = self.auth_config.get_auth_headers()
        headers.update(auth_headers)

        return headers

    async def _test_server_connection(
        self, server_config: ServerConfig, headers: Dict[str, str]
    ) -> None:
        """
        Test server connection with a proper MCP initialization request.

        Args:
            server_config: Server configuration
            headers: HTTP headers to use

        Raises:
            MCPAuthenticationError: If authentication fails
            MCPConnectionError: If connection fails
        """
        server_name = server_config.name
        server_url = server_config.url

        async with httpx.AsyncClient(timeout=server_config.timeout) as client:
            try:
                # Send a proper MCP initialization message for authentication test
                test_message = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "initialize",
                    "params": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {},
                        "clientInfo": {
                            "name": "minimal-mcp-client",
                            "version": "0.1.0",
                        },
                    },
                }
                response = await client.post(
                    server_url, json=test_message, headers=headers
                )
                response.raise_for_status()
                logger.info(f"Authentication test successful for '{server_name}'")

            except httpx.HTTPStatusError as e:
                if e.response.status_code == 401:
                    auth_challenge = e.response.headers.get("WWW-Authenticate")
                    if self.auth_config.bearer_token:
                        error_msg = "Bearer token authentication failed"
                    else:
                        error_msg = (
                            "Server requires authentication but no token provided"
                        )

                    raise MCPAuthenticationError(
                        error_msg,
                        server_name=server_name,
                        auth_challenge=auth_challenge,
                    ) from e

                elif e.response.status_code == 406:
                    raise MCPConnectionError(
                        "Server returned 406 Not Acceptable - "
                        "server may not support MCP protocol or requires different headers",
                        server_name=server_name,
                        server_url=server_url,
                    ) from e
                else:
                    raise MCPServerError(
                        f"Server returned HTTP {e.response.status_code}",
                        server_name=server_name,
                        status_code=e.response.status_code,
                    ) from e

    async def discover_capabilities(self, server_name: str) -> Dict[str, Any]:
        """
        Discover the capabilities (tools, resources, prompts) of a connected server.

        Args:
            server_name: Name of the connected server

        Returns:
            Dictionary containing discovered capabilities

        Raises:
            MCPClientError: If server is not connected or discovery fails
        """
        if server_name not in self.connected_servers:
            raise MCPClientError(f"Server '{server_name}' is not connected")

        server_info = self.connected_servers[server_name]
        session = server_info["session"]

        capabilities = {"tools": [], "resources": [], "prompts": []}

        try:
            # Discover tools
            logger.info(f"Discovering tools from '{server_name}'...")
            tools_result = await session.list_tools()
            capabilities["tools"] = tools_result.tools
            server_info["tools"] = tools_result.tools
            logger.info(f"Found {len(tools_result.tools)} tools")

            # Discover resources
            logger.info(f"Discovering resources from '{server_name}'...")
            resources_result = await session.list_resources()
            capabilities["resources"] = resources_result.resources
            server_info["resources"] = resources_result.resources
            logger.info(f"Found {len(resources_result.resources)} resources")

            # Discover prompts
            logger.info(f"Discovering prompts from '{server_name}'...")
            prompts_result = await session.list_prompts()
            capabilities["prompts"] = prompts_result.prompts
            server_info["prompts"] = prompts_result.prompts
            logger.info(f"Found {len(prompts_result.prompts)} prompts")

        except Exception as e:
            logger.error(f"Error discovering capabilities from '{server_name}': {e}")
            raise MCPClientError(
                f"Failed to discover capabilities: {e}", server_name=server_name
            ) from e

        return capabilities

    async def call_tool(
        self, server_name: str, tool_name: str, arguments: Dict[str, Any]
    ) -> Any:
        """
        Call a tool on a connected MCP server.

        Args:
            server_name: Name of the connected server
            tool_name: Name of the tool to call
            arguments: Arguments to pass to the tool

        Returns:
            The result from the tool call

        Raises:
            MCPClientError: If server is not connected
            MCPToolError: If tool call fails
        """
        if server_name not in self.connected_servers:
            raise MCPClientError(f"Server '{server_name}' is not connected")

        server_info = self.connected_servers[server_name]
        session = server_info["session"]

        try:
            logger.info(
                f"Calling tool '{tool_name}' on '{server_name}' with args: {arguments}"
            )
            result = await session.call_tool(tool_name, arguments)
            logger.info("Tool call successful")
            return result

        except Exception as e:
            logger.error(f"Error calling tool '{tool_name}' on '{server_name}': {e}")
            raise MCPToolError(
                f"Tool call failed: {e}",
                server_name=server_name,
                tool_name=tool_name,
                arguments=arguments,
            ) from e

    async def read_resource(self, server_name: str, resource_uri: str) -> Any:
        """
        Read a resource from a connected MCP server.

        Args:
            server_name: Name of the connected server
            resource_uri: URI of the resource to read

        Returns:
            The resource content

        Raises:
            MCPClientError: If server is not connected
            MCPResourceError: If resource read fails
        """
        if server_name not in self.connected_servers:
            raise MCPClientError(f"Server '{server_name}' is not connected")

        server_info = self.connected_servers[server_name]
        session = server_info["session"]

        try:
            logger.info(f"Reading resource '{resource_uri}' from '{server_name}'")
            result = await session.read_resource(resource_uri)
            logger.info("Resource read successful")
            return result

        except Exception as e:
            logger.error(
                f"Error reading resource '{resource_uri}' from '{server_name}': {e}"
            )
            raise MCPResourceError(
                f"Resource read failed: {e}",
                server_name=server_name,
                resource_uri=resource_uri,
            ) from e

    def get_server_info(self, server_name: str) -> Dict[str, Any]:
        """
        Get information about a connected server.

        Args:
            server_name: Name of the connected server

        Returns:
            Dictionary containing server information

        Raises:
            MCPClientError: If server is not connected
        """
        if server_name not in self.connected_servers:
            raise MCPClientError(f"Server '{server_name}' is not connected")

        server_info = self.connected_servers[server_name].copy()
        # Remove session object for serialization
        server_info.pop("session", None)
        return server_info

    def list_connected_servers(self) -> List[str]:
        """
        Get a list of connected server names.

        Returns:
            List of connected server names
        """
        return list(self.connected_servers.keys())

    def is_connected(self, server_name: str) -> bool:
        """
        Check if a server is connected.

        Args:
            server_name: Name of the server to check

        Returns:
            True if server is connected, False otherwise
        """
        return server_name in self.connected_servers

    async def disconnect_server(self, server_name: str) -> None:
        """
        Disconnect from a specific server.

        Args:
            server_name: Name of the server to disconnect from

        Raises:
            MCPClientError: If server is not connected
        """
        if server_name not in self.connected_servers:
            raise MCPClientError(f"Server '{server_name}' is not connected")

        logger.info(f"Disconnecting from server '{server_name}'...")
        # Remove from connected servers - the exit stack will handle cleanup
        del self.connected_servers[server_name]
        logger.info(f"Disconnected from '{server_name}'")

    async def disconnect_all(self) -> None:
        """Disconnect from all servers and clean up resources."""
        logger.info("Disconnecting from all servers...")
        await self.exit_stack.aclose()
        self.connected_servers.clear()
        logger.info("All connections closed")
