#!/usr/bin/env python3
"""
MCP Server Configuration Management Utility.

This script provides a command-line interface for managing MCP server configurations
stored in the JSON configuration file.
"""

import argparse
import json
import sys
from typing import List, Optional

from mcp_client import JSONConfigManager, ServerConfig
from mcp_client.exceptions import MCPConfigurationError


def list_servers(manager: JSONConfigManager) -> None:
    """List all configured servers."""
    servers = manager.list_servers()
    
    if not servers:
        print("No servers configured.")
        return
    
    print(f"Configured servers ({len(servers)}):")
    print("-" * 40)
    
    for server_name in servers:
        try:
            config = manager.get_server_config(server_name)
            tools = manager.get_server_tools(server_name)
            
            print(f"Name: {config.name}")
            print(f"URL: {config.url}")
            print(f"Description: {config.description}")
            print(f"Demo Input: {config.demo_input}")
            print(f"Timeout: {config.timeout}s")
            print(f"Max Retries: {config.max_retries}")
            print(f"Tools: {', '.join(tools) if tools else 'None'}")
            print("-" * 40)
        except MCPConfigurationError as e:
            print(f"Error loading {server_name}: {e}")


def add_server(
    manager: JSONConfigManager,
    name: str,
    url: str,
    description: str = "",
    demo_input: str = "",
    timeout: float = 30.0,
    max_retries: int = 3,
    tools: Optional[List[str]] = None
) -> None:
    """Add a new server configuration."""
    try:
        server_config = ServerConfig(
            name=name,
            url=url,
            description=description,
            demo_input=demo_input,
            timeout=timeout,
            max_retries=max_retries
        )
        
        manager.add_server(server_config, tools)
        print(f"✅ Successfully added server '{name}'")
        
    except MCPConfigurationError as e:
        print(f"❌ Error adding server: {e}")
        sys.exit(1)


def remove_server(manager: JSONConfigManager, name: str) -> None:
    """Remove a server configuration."""
    try:
        manager.remove_server(name)
        print(f"✅ Successfully removed server '{name}'")
        
    except MCPConfigurationError as e:
        print(f"❌ Error removing server: {e}")
        sys.exit(1)


def update_server(manager: JSONConfigManager, name: str, **kwargs) -> None:
    """Update a server configuration."""
    try:
        # Filter out None values
        updates = {k: v for k, v in kwargs.items() if v is not None}
        
        if not updates:
            print("No updates provided.")
            return
        
        manager.update_server(name, **updates)
        print(f"✅ Successfully updated server '{name}'")
        
        # Show updated configuration
        config = manager.get_server_config(name)
        print(f"Updated configuration:")
        print(f"  URL: {config.url}")
        print(f"  Description: {config.description}")
        print(f"  Demo Input: {config.demo_input}")
        print(f"  Timeout: {config.timeout}s")
        print(f"  Max Retries: {config.max_retries}")
        
    except MCPConfigurationError as e:
        print(f"❌ Error updating server: {e}")
        sys.exit(1)


def show_metadata(manager: JSONConfigManager) -> None:
    """Show configuration file metadata."""
    metadata = manager.get_config_metadata()
    
    print("Configuration Metadata:")
    print("-" * 30)
    for key, value in metadata.items():
        print(f"{key.replace('_', ' ').title()}: {value}")


def export_config(manager: JSONConfigManager, output_file: str) -> None:
    """Export configuration to a file."""
    try:
        manager.export_config(output_file)
        print(f"✅ Configuration exported to '{output_file}'")
        
    except MCPConfigurationError as e:
        print(f"❌ Error exporting configuration: {e}")
        sys.exit(1)


def main() -> None:
    """Main entry point for the configuration management utility."""
    parser = argparse.ArgumentParser(
        description="Manage MCP server configurations",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s list                                    # List all servers
  %(prog)s add myserver https://example.com/mcp   # Add a server
  %(prog)s remove myserver                         # Remove a server
  %(prog)s update myserver --description "New desc" # Update server
  %(prog)s metadata                                # Show metadata
  %(prog)s export backup.json                     # Export config
        """
    )
    
    parser.add_argument(
        "--config-file",
        default="mcp_servers.json",
        help="Path to the JSON configuration file (default: mcp_servers.json)"
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # List command
    subparsers.add_parser("list", help="List all configured servers")
    
    # Add command
    add_parser = subparsers.add_parser("add", help="Add a new server")
    add_parser.add_argument("name", help="Server name")
    add_parser.add_argument("url", help="Server URL")
    add_parser.add_argument("--description", default="", help="Server description")
    add_parser.add_argument("--demo-input", default="", help="Demo input for testing")
    add_parser.add_argument("--timeout", type=float, default=30.0, help="Request timeout")
    add_parser.add_argument("--max-retries", type=int, default=3, help="Maximum retries")
    add_parser.add_argument("--tools", nargs="*", help="List of available tools")
    
    # Remove command
    remove_parser = subparsers.add_parser("remove", help="Remove a server")
    remove_parser.add_argument("name", help="Server name to remove")
    
    # Update command
    update_parser = subparsers.add_parser("update", help="Update a server")
    update_parser.add_argument("name", help="Server name to update")
    update_parser.add_argument("--url", help="New server URL")
    update_parser.add_argument("--description", help="New server description")
    update_parser.add_argument("--demo-input", help="New demo input")
    update_parser.add_argument("--timeout", type=float, help="New timeout")
    update_parser.add_argument("--max-retries", type=int, help="New max retries")
    update_parser.add_argument("--tools", nargs="*", help="New list of tools")
    
    # Metadata command
    subparsers.add_parser("metadata", help="Show configuration metadata")
    
    # Export command
    export_parser = subparsers.add_parser("export", help="Export configuration")
    export_parser.add_argument("output_file", help="Output file path")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        manager = JSONConfigManager(args.config_file)
    except MCPConfigurationError as e:
        print(f"❌ Error loading configuration: {e}")
        sys.exit(1)
    
    if args.command == "list":
        list_servers(manager)
    elif args.command == "add":
        add_server(
            manager,
            args.name,
            args.url,
            args.description,
            args.demo_input,
            args.timeout,
            args.max_retries,
            args.tools
        )
    elif args.command == "remove":
        remove_server(manager, args.name)
    elif args.command == "update":
        update_server(
            manager,
            args.name,
            url=args.url,
            description=args.description,
            demo_input=args.demo_input,
            timeout=args.timeout,
            max_retries=args.max_retries,
            tools=args.tools
        )
    elif args.command == "metadata":
        show_metadata(manager)
    elif args.command == "export":
        export_config(manager, args.output_file)


if __name__ == "__main__":
    main()
