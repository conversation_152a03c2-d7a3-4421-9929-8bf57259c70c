# JSON Configuration System for MCP Servers

This document describes the JSON-based configuration system for managing MCP (Model Context Protocol) servers in the minimal-pocketflow-mcp project.

## Overview

The JSON configuration system provides a flexible and persistent way to manage MCP server configurations. It replaces the previous hardcoded server configurations with a dynamic system that supports adding, removing, and updating server configurations at runtime.

## Configuration File Structure

The configuration is stored in `mcp_servers.json` with the following structure:

```json
{
  "servers": {
    "server_name": {
      "name": "server_name",
      "url": "https://example.com/mcp",
      "description": "Server description",
      "demo_input": "Example input for testing",
      "timeout": 30.0,
      "max_retries": 3,
      "tools": ["tool1", "tool2", "tool3"]
    }
  },
  "metadata": {
    "version": "1.0.0",
    "last_updated": "2025-01-04T00:00:00Z",
    "description": "MCP Server Configuration File"
  }
}
```

### Server Configuration Fields

- **name**: Unique identifier for the server
- **url**: MCP server endpoint URL
- **description**: Human-readable description of the server
- **demo_input**: Example input for testing the server
- **timeout**: Request timeout in seconds (default: 30.0)
- **max_retries**: Maximum number of retry attempts (default: 3)
- **tools**: List of available tools provided by the server (optional)

## Default Configuration

The system comes with pre-configured servers:

### CubeMCP Server
- **URL**: `https://mcp.cubeflow.cn/api/mcp/`
- **Description**: 集成多种工具和提示词的mcp server
- **Tools**: esop, notice_data, buyback_data, crawl_tool, file_read_tool, search_tool, stock_data_search

### Code Interpreter Server
- **URL**: `https://mcp.cubeflow.cn/api/code_interpreter`
- **Description**: Code interpreter MCP server for executing code and analysis
- **Tools**: execute_code, analyze_code, format_code

## Usage

### Programmatic Usage

#### Using JSONConfigManager Directly

```python
from mcp_client import JSONConfigManager, ServerConfig

# Initialize manager
manager = JSONConfigManager("mcp_servers.json")

# Add a new server
server_config = ServerConfig(
    name="my_server",
    url="https://my-server.com/mcp",
    description="My custom MCP server",
    demo_input="test input",
    timeout=45.0,
    max_retries=5
)

manager.add_server(server_config, tools=["tool1", "tool2"])

# List all servers
servers = manager.list_servers()
print(f"Configured servers: {', '.join(servers)}")

# Get server configuration
config = manager.get_server_config("my_server")
print(f"Server URL: {config.url}")

# Get server tools
tools = manager.get_server_tools("my_server")
print(f"Available tools: {', '.join(tools)}")

# Update server
manager.update_server("my_server", description="Updated description")

# Remove server
manager.remove_server("my_server")
```

#### Using MCPConfig (Recommended)

```python
from mcp_client import MCPConfig, ServerConfig

# Initialize with custom config file
config = MCPConfig(config_file="my_servers.json")

# Add server
server_config = ServerConfig(
    name="my_server",
    url="https://my-server.com/mcp",
    description="My custom MCP server"
)

config.add_server(server_config, tools=["tool1", "tool2"])

# Use with server manager
from mcp_client import MCPServerManager

server_manager = MCPServerManager(config)
await server_manager.connect_to_all_servers()
```

### Command-Line Usage

The `manage_servers.py` script provides a command-line interface for managing server configurations:

#### List all servers
```bash
python manage_servers.py list
```

#### Add a new server
```bash
python manage_servers.py add my_server https://my-server.com/mcp \
    --description "My custom server" \
    --demo-input "test input" \
    --timeout 45 \
    --max-retries 5 \
    --tools tool1 tool2 tool3
```

#### Update a server
```bash
python manage_servers.py update my_server \
    --description "Updated description" \
    --timeout 60
```

#### Remove a server
```bash
python manage_servers.py remove my_server
```

#### Show configuration metadata
```bash
python manage_servers.py metadata
```

#### Export configuration
```bash
python manage_servers.py export backup.json
```

## API Reference

### JSONConfigManager

#### Methods

- `add_server(server_config: ServerConfig, tools: Optional[List[str]] = None)`: Add a new server
- `remove_server(server_name: str)`: Remove a server
- `update_server(server_name: str, **kwargs)`: Update server configuration
- `get_server_config(server_name: str) -> ServerConfig`: Get server configuration
- `list_servers() -> List[str]`: List all server names
- `get_server_tools(server_name: str) -> List[str]`: Get server tools
- `get_config_metadata() -> Dict[str, Any]`: Get configuration metadata
- `export_config(output_file: str)`: Export configuration to file

### MCPConfig Extensions

The existing `MCPConfig` class has been extended with new methods:

- `update_server(server_name: str, **kwargs)`: Update server configuration
- `get_server_tools(server_name: str) -> List[str]`: Get server tools
- `get_config_metadata() -> Dict[str, Any]`: Get configuration metadata
- `export_config(output_file: str)`: Export configuration

## Error Handling

The system raises `MCPConfigurationError` for various error conditions:

- Server not found
- Duplicate server names
- Invalid configuration data
- File I/O errors

## Testing

Run the test script to verify the configuration system:

```bash
python test_json_config.py
```

This will test:
- JSON configuration manager functionality
- Integration with MCPConfig
- Error handling scenarios

## Migration from Hardcoded Configuration

The system automatically migrates from the previous hardcoded configuration. When you first run the application with the new system:

1. The existing hardcoded servers are preserved in the JSON file
2. You can add, remove, and modify servers using the new system
3. The old hardcoded configuration is no longer used

## Best Practices

1. **Backup Configuration**: Regularly backup your `mcp_servers.json` file
2. **Version Control**: Include the configuration file in version control
3. **Environment-Specific Configs**: Use different config files for different environments
4. **Validation**: Always validate server URLs and configurations before adding them
5. **Documentation**: Document custom servers and their capabilities

## Troubleshooting

### Configuration File Not Found
If the configuration file doesn't exist, the system will create a default one with the pre-configured servers.

### Invalid JSON Format
If the JSON file is corrupted, the system will raise an `MCPConfigurationError`. Restore from backup or recreate the file.

### Server Connection Issues
Use the demo_input field to test server connectivity and verify that URLs are correct.

### Permission Issues
Ensure the application has read/write permissions for the configuration file directory.
