"""
JSON Configuration Manager for MCP Servers.

This module provides functionality to manage MCP server configurations
stored in a JSON file, including adding, removing, and updating server metadata.
"""

import json
import os
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

from .exceptions import MCPConfigurationError
from .models import ServerConfig


class JSONConfigManager:
    """
    Manages MCP server configurations stored in a JSON file.

    This class provides methods to load, save, add, remove, and list
    MCP server configurations from a JSON configuration file.
    """

    def __init__(self, config_file: str = "mcp_servers.json") -> None:
        """
        Initialize the JSON configuration manager.

        Args:
            config_file: Path to the JSON configuration file
        """
        self.config_file = Path(config_file)
        self._config_data: Dict[str, Any] = {}
        self._load_config()

    def _load_config(self) -> None:
        """Load configuration from JSON file."""
        if not self.config_file.exists():
            # Create default configuration if file doesn't exist
            self._config_data = {
                "servers": {},
                "metadata": {
                    "version": "1.0.0",
                    "last_updated": datetime.now(timezone.utc).isoformat(),
                    "description": "MCP Server Configuration File - manages available MCP servers and their metadata",
                },
            }
            self._save_config()
            return

        try:
            with open(self.config_file, "r", encoding="utf-8") as f:
                self._config_data = json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            raise MCPConfigurationError(
                f"Failed to load configuration file {self.config_file}: {e}"
            )

        # Validate configuration structure
        if "servers" not in self._config_data:
            self._config_data["servers"] = {}
        if "metadata" not in self._config_data:
            self._config_data["metadata"] = {
                "version": "1.0.0",
                "last_updated": datetime.now(timezone.utc).isoformat(),
                "description": "MCP Server Configuration File",
            }

    def _save_config(self) -> None:
        """Save configuration to JSON file."""
        # Update last_updated timestamp
        self._config_data["metadata"]["last_updated"] = datetime.now(
            timezone.utc
        ).isoformat()

        try:
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(self._config_data, f, indent=2, ensure_ascii=False)
        except IOError as e:
            raise MCPConfigurationError(
                f"Failed to save configuration file {self.config_file}: {e}"
            )

    def add_server(
        self, server_config: ServerConfig, tools: Optional[List[str]] = None
    ) -> None:
        """
        Add a new server configuration.

        Args:
            server_config: Server configuration to add
            tools: Optional list of available tools for this server

        Raises:
            MCPConfigurationError: If server already exists
        """
        if server_config.name in self._config_data["servers"]:
            raise MCPConfigurationError(f"Server '{server_config.name}' already exists")

        server_data = {
            "name": server_config.name,
            "url": server_config.url,
            "description": server_config.description,
            "demo_input": server_config.demo_input,
            "timeout": server_config.timeout,
            "max_retries": server_config.max_retries,
        }

        if tools:
            server_data["tools"] = tools

        self._config_data["servers"][server_config.name] = server_data
        self._save_config()

    def remove_server(self, server_name: str) -> None:
        """
        Remove a server configuration.

        Args:
            server_name: Name of the server to remove

        Raises:
            MCPConfigurationError: If server doesn't exist
        """
        if server_name not in self._config_data["servers"]:
            raise MCPConfigurationError(f"Server '{server_name}' not found")

        del self._config_data["servers"][server_name]
        self._save_config()

    def update_server(self, server_name: str, **kwargs: Any) -> None:
        """
        Update an existing server configuration.

        Args:
            server_name: Name of the server to update
            **kwargs: Fields to update (url, description, demo_input, timeout, max_retries, tools)

        Raises:
            MCPConfigurationError: If server doesn't exist
        """
        if server_name not in self._config_data["servers"]:
            raise MCPConfigurationError(f"Server '{server_name}' not found")

        server_data = self._config_data["servers"][server_name]

        # Update allowed fields
        allowed_fields = {
            "url",
            "description",
            "demo_input",
            "timeout",
            "max_retries",
            "tools",
        }
        for key, value in kwargs.items():
            if key in allowed_fields:
                server_data[key] = value

        self._save_config()

    def get_server_config(self, server_name: str) -> ServerConfig:
        """
        Get configuration for a specific server.

        Args:
            server_name: Name of the server

        Returns:
            Server configuration object

        Raises:
            MCPConfigurationError: If server doesn't exist
        """
        if server_name not in self._config_data["servers"]:
            raise MCPConfigurationError(f"Server '{server_name}' not found")

        server_data = self._config_data["servers"][server_name]

        return ServerConfig(
            name=server_data["name"],
            url=server_data["url"],
            description=server_data.get("description", ""),
            demo_input=server_data.get("demo_input", ""),
            timeout=server_data.get("timeout", 30.0),
            max_retries=server_data.get("max_retries", 3),
        )

    def list_servers(self) -> List[str]:
        """
        Get list of configured server names.

        Returns:
            List of server names
        """
        return list(self._config_data["servers"].keys())

    def get_all_server_configs(self) -> Dict[str, ServerConfig]:
        """
        Get all server configurations.

        Returns:
            Dictionary mapping server names to ServerConfig objects
        """
        configs = {}
        for server_name in self._config_data["servers"]:
            configs[server_name] = self.get_server_config(server_name)
        return configs

    def get_server_tools(self, server_name: str) -> List[str]:
        """
        Get list of tools available for a specific server.

        Args:
            server_name: Name of the server

        Returns:
            List of tool names

        Raises:
            MCPConfigurationError: If server doesn't exist
        """
        if server_name not in self._config_data["servers"]:
            raise MCPConfigurationError(f"Server '{server_name}' not found")

        server_data = self._config_data["servers"][server_name]
        return server_data.get("tools", [])

    def get_config_metadata(self) -> Dict[str, Any]:
        """
        Get configuration file metadata.

        Returns:
            Dictionary containing metadata information
        """
        return self._config_data["metadata"].copy()

    def export_config(self, output_file: str) -> None:
        """
        Export current configuration to a different file.

        Args:
            output_file: Path to the output file
        """
        try:
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(self._config_data, f, indent=2, ensure_ascii=False)
        except IOError as e:
            raise MCPConfigurationError(
                f"Failed to export configuration to {output_file}: {e}"
            )
