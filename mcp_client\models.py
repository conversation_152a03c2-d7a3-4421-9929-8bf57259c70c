"""
Data models for MCP client configuration.

This module contains the data classes used for MCP server and client configuration.
"""

from dataclasses import dataclass
from urllib.parse import urlparse

from .exceptions import MCPConfigurationError


@dataclass
class ServerConfig:
    """Configuration for a single MCP server."""

    name: str
    url: str
    description: str = ""
    demo_input: str = ""
    timeout: float = 30.0
    max_retries: int = 3

    def __post_init__(self) -> None:
        """Validate server configuration after initialization."""
        self._validate()

    def _validate(self) -> None:
        """Validate the server configuration."""
        if not self.name:
            raise MCPConfigurationError("Server name cannot be empty")

        if not self.url:
            raise MCPConfigurationError(f"Server URL cannot be empty for '{self.name}'")

        # Validate URL format
        try:
            parsed = urlparse(self.url)
            if not parsed.scheme or not parsed.netloc:
                raise MCPConfigurationError(
                    f"Invalid URL format for server '{self.name}': {self.url}"
                )
        except Exception as e:
            raise MCPConfigurationError(f"Invalid URL for server '{self.name}': {e}")

        if self.timeout <= 0:
            raise MCPConfigurationError(
                f"Timeout must be positive for server '{self.name}'"
            )

        if self.max_retries < 0:
            raise MCPConfigurationError(
                f"Max retries cannot be negative for server '{self.name}'"
            )


@dataclass
class AuthConfig:
    """Authentication configuration for MCP requests."""

    bearer_token: str = ""
    api_key: str = ""
    custom_headers: dict = None

    def __post_init__(self) -> None:
        """Initialize custom headers and process bearer token."""
        if self.custom_headers is None:
            self.custom_headers = {}

        # Convert None values to empty strings
        if self.bearer_token is None:
            self.bearer_token = ""
        if self.api_key is None:
            self.api_key = ""

        # Process bearer token format
        if self.bearer_token and not self.bearer_token.startswith("Bearer "):
            self.bearer_token = f"Bearer {self.bearer_token}"

    def get_auth_headers(self) -> dict:
        """
        Get authentication headers for HTTP requests.

        Returns:
            Dictionary of headers to include in requests
        """
        headers = self.custom_headers.copy()

        if self.bearer_token:
            headers["Authorization"] = self.bearer_token
        elif self.api_key:
            headers["X-API-Key"] = self.api_key

        return headers


@dataclass
class ClientConfig:
    """Client-wide configuration settings."""

    name: str = "minimal-mcp-client"
    version: str = "0.1.0"
    protocol_version: str = "2024-11-05"
    default_timeout: float = 30.0
    max_concurrent_connections: int = 10
    log_level: str = "INFO"

    def __post_init__(self) -> None:
        """Validate client configuration after initialization."""
        self._validate()

    def _validate(self) -> None:
        """Validate the client configuration."""
        if self.default_timeout <= 0:
            raise MCPConfigurationError("Default timeout must be positive")

        if self.max_concurrent_connections <= 0:
            raise MCPConfigurationError("Max concurrent connections must be positive")

        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.log_level not in valid_log_levels:
            raise MCPConfigurationError(
                f"Invalid log level: {self.log_level}. "
                f"Must be one of: {', '.join(valid_log_levels)}"
            )
