"""
Test MCP exception handling.
"""

import pytest

from mcp_client.exceptions import (
    MCPClientError,
    MCPConnectionError,
    MCPAuthenticationError,
    MCPServerError,
    MCPConfigurationError,
    MCPToolError,
    MCPResourceError,
)


class TestMCPExceptions:
    """Test MCP exception hierarchy and functionality."""

    def test_base_exception(self):
        """Test base MCP exception."""
        error = MCPClientError("Test error")
        assert str(error) == "Test error"
        assert isinstance(error, Exception)

    def test_connection_error(self):
        """Test connection error."""
        error = MCPConnectionError("Connection failed", server_name="test-server")
        assert "Connection failed" in str(error)
        assert error.server_name == "test-server"
        assert isinstance(error, MCPClientError)

    def test_authentication_error(self):
        """Test authentication error."""
        error = MCPAuthenticationError("Auth failed", server_name="test-server")
        assert "Auth failed" in str(error)
        assert error.server_name == "test-server"
        assert isinstance(error, MCPClientError)

    def test_server_error(self):
        """Test server error."""
        error = MCPServerError(
            "Server error", server_name="test-server", status_code=500
        )
        assert "Server error" in str(error)
        assert error.server_name == "test-server"
        assert error.status_code == 500
        assert isinstance(error, MCPClientError)

    def test_configuration_error(self):
        """Test configuration error."""
        error = MCPConfigurationError("Config error", config_key="test-key")
        assert "Config error" in str(error)
        assert error.config_key == "test-key"
        assert isinstance(error, MCPClientError)

    def test_tool_error(self):
        """Test tool error."""
        error = MCPToolError(
            "Tool error", tool_name="test-tool", server_name="test-server"
        )
        assert "Tool error" in str(error)
        assert error.tool_name == "test-tool"
        assert error.server_name == "test-server"
        assert isinstance(error, MCPClientError)

    def test_resource_error(self):
        """Test resource error."""
        error = MCPResourceError(
            "Resource error", resource_uri="test://resource", server_name="test-server"
        )
        assert "Resource error" in str(error)
        assert error.resource_uri == "test://resource"
        assert error.server_name == "test-server"
        assert isinstance(error, MCPClientError)

    def test_exception_hierarchy(self):
        """Test that all exceptions inherit from MCPClientError."""
        exceptions = [
            MCPConnectionError("test"),
            MCPAuthenticationError("test"),
            MCPServerError("test"),
            MCPConfigurationError("test"),
            MCPToolError("test"),
            MCPResourceError("test"),
        ]

        for exc in exceptions:
            assert isinstance(exc, MCPClientError)
            assert isinstance(exc, Exception)

    def test_exception_with_cause(self):
        """Test exceptions with underlying causes."""
        original_error = ValueError("Original error")

        try:
            raise original_error
        except ValueError as e:
            mcp_error = MCPConnectionError("Connection failed")
            mcp_error.__cause__ = e
            assert mcp_error.__cause__ is original_error

    def test_exception_context_preservation(self):
        """Test that exception context is preserved."""
        error = MCPToolError(
            "Tool execution failed",
            tool_name="test-tool",
            server_name="test-server",
            arguments={"param1": "value1"},
        )

        assert error.tool_name == "test-tool"
        assert error.server_name == "test-server"
        assert error.arguments == {"param1": "value1"}

    def test_exception_string_representation(self):
        """Test string representation of exceptions."""
        error = MCPServerError(
            "Internal server error", server_name="test-server", status_code=500
        )

        error_str = str(error)
        assert "Internal server error" in error_str
        assert "test-server" in error_str or "500" in error_str

    def test_exception_optional_parameters(self):
        """Test exceptions with optional parameters."""
        # Test with minimal parameters
        error1 = MCPConnectionError("Connection failed")
        assert error1.server_name is None

        # Test with all parameters
        error2 = MCPConnectionError("Connection failed", server_name="test-server")
        assert error2.server_name == "test-server"
