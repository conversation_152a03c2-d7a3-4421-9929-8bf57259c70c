#!/usr/bin/env python3
"""
Test script for the JSON configuration system.

This script demonstrates the functionality of the JSON configuration manager
and verifies that it works correctly with the existing MCP client system.
"""

import asyncio
import os
import tempfile
from pathlib import Path

from mcp_client import J<PERSON><PERSON>onfigManager, MCPConfig, ServerConfig
from mcp_client.exceptions import MCPConfigurationError


async def test_json_config_manager():
    """Test the JSON configuration manager functionality."""
    print("🧪 Testing JSON Configuration Manager")
    print("=" * 50)

    # Create a temporary config file for testing
    with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
        temp_config_file = f.name
        # Write empty JSON to avoid parsing errors
        f.write("{}")
        f.flush()

    try:
        # Initialize manager with temporary file
        manager = JSONConfigManager(temp_config_file)

        print("✅ JSON Configuration Manager initialized")

        # Test adding servers
        print("\n📝 Testing server addition...")

        # Add the example server from the requirements
        code_interpreter_config = ServerConfig(
            name="code_interpreter",
            url="https://mcp.cubeflow.cn/api/code_interpreter",
            description="Code interpreter MCP server for executing code and analysis",
            demo_input="print('Hello, World!')",
            timeout=45.0,
            max_retries=3,
        )

        manager.add_server(
            code_interpreter_config,
            tools=["execute_code", "analyze_code", "format_code"],
        )
        print("✅ Added code_interpreter server")

        # Add another test server
        test_server_config = ServerConfig(
            name="test_server",
            url="https://example.com/mcp",
            description="Test MCP server",
            demo_input="test input",
            timeout=30.0,
            max_retries=2,
        )

        manager.add_server(test_server_config, tools=["test_tool1", "test_tool2"])
        print("✅ Added test_server")

        # Test listing servers
        print("\n📋 Testing server listing...")
        servers = manager.list_servers()
        print(f"Found {len(servers)} servers: {', '.join(servers)}")

        # Test getting server config
        print("\n🔍 Testing server configuration retrieval...")
        config = manager.get_server_config("code_interpreter")
        print(f"Retrieved config for {config.name}:")
        print(f"  URL: {config.url}")
        print(f"  Description: {config.description}")
        print(f"  Timeout: {config.timeout}s")

        # Test getting server tools
        print("\n🔧 Testing server tools retrieval...")
        tools = manager.get_server_tools("code_interpreter")
        print(f"Tools for code_interpreter: {', '.join(tools)}")

        # Test updating server
        print("\n✏️ Testing server update...")
        manager.update_server(
            "test_server", description="Updated test MCP server", timeout=60.0
        )

        updated_config = manager.get_server_config("test_server")
        print(f"Updated test_server:")
        print(f"  Description: {updated_config.description}")
        print(f"  Timeout: {updated_config.timeout}s")

        # Test metadata
        print("\n📊 Testing metadata retrieval...")
        metadata = manager.get_config_metadata()
        print(f"Config version: {metadata['version']}")
        print(f"Last updated: {metadata['last_updated']}")

        # Test export
        print("\n💾 Testing configuration export...")
        export_file = temp_config_file + ".export"
        manager.export_config(export_file)
        print(f"✅ Configuration exported to {export_file}")

        # Test removing server
        print("\n🗑️ Testing server removal...")
        manager.remove_server("test_server")
        remaining_servers = manager.list_servers()
        print(f"Remaining servers after removal: {', '.join(remaining_servers)}")

        print("\n✅ All JSON Configuration Manager tests passed!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise
    finally:
        # Clean up temporary files
        for file_path in [temp_config_file, temp_config_file + ".export"]:
            if os.path.exists(file_path):
                os.unlink(file_path)


async def test_mcp_config_integration():
    """Test integration with the existing MCPConfig class."""
    print("\n🔗 Testing MCPConfig Integration")
    print("=" * 50)

    # Create a temporary config file for testing
    with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
        temp_config_file = f.name
        # Write empty JSON to avoid parsing errors
        f.write("{}")
        f.flush()

    try:
        # Initialize MCPConfig with custom config file
        config = MCPConfig(load_env=False, config_file=temp_config_file)

        print("✅ MCPConfig initialized with JSON backend")

        # Test that it starts with the default servers from the JSON file
        servers = config.list_servers()
        print(f"Initial servers: {', '.join(servers)}")

        # Add a server through MCPConfig
        print("\n📝 Testing server addition through MCPConfig...")
        new_server = ServerConfig(
            name="integration_test",
            url="https://integration.test/mcp",
            description="Integration test server",
            demo_input="integration test",
            timeout=25.0,
            max_retries=1,
        )

        config.add_server(new_server, tools=["integration_tool"])

        # Verify it was added
        servers = config.list_servers()
        print(f"Servers after addition: {', '.join(servers)}")

        # Test getting server config through MCPConfig
        retrieved_config = config.get_server_config("integration_test")
        print(f"Retrieved integration_test config:")
        print(f"  URL: {retrieved_config.url}")
        print(f"  Description: {retrieved_config.description}")

        # Test getting server tools
        tools = config.get_server_tools("integration_test")
        print(f"Tools: {', '.join(tools)}")

        # Test updating server through MCPConfig
        print("\n✏️ Testing server update through MCPConfig...")
        config.update_server(
            "integration_test",
            description="Updated integration test server",
            tools=["updated_tool1", "updated_tool2"],
        )

        updated_config = config.get_server_config("integration_test")
        updated_tools = config.get_server_tools("integration_test")
        print(f"Updated config description: {updated_config.description}")
        print(f"Updated tools: {', '.join(updated_tools)}")

        # Test removing server through MCPConfig
        print("\n🗑️ Testing server removal through MCPConfig...")
        config.remove_server("integration_test")

        remaining_servers = config.list_servers()
        print(f"Remaining servers: {', '.join(remaining_servers)}")

        print("\n✅ All MCPConfig integration tests passed!")

    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        raise
    finally:
        # Clean up temporary file
        if os.path.exists(temp_config_file):
            os.unlink(temp_config_file)


async def test_error_handling():
    """Test error handling scenarios."""
    print("\n⚠️ Testing Error Handling")
    print("=" * 50)

    with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
        temp_config_file = f.name
        # Write empty JSON to avoid parsing errors
        f.write("{}")
        f.flush()

    try:
        manager = JSONConfigManager(temp_config_file)

        # Test getting non-existent server
        print("Testing non-existent server retrieval...")
        try:
            manager.get_server_config("non_existent")
            print("❌ Should have raised an error")
        except MCPConfigurationError:
            print("✅ Correctly raised MCPConfigurationError for non-existent server")

        # Test removing non-existent server
        print("Testing non-existent server removal...")
        try:
            manager.remove_server("non_existent")
            print("❌ Should have raised an error")
        except MCPConfigurationError:
            print(
                "✅ Correctly raised MCPConfigurationError for non-existent server removal"
            )

        # Test adding duplicate server
        print("Testing duplicate server addition...")
        test_config = ServerConfig(
            name="duplicate_test", url="https://test.com/mcp", description="Test server"
        )

        manager.add_server(test_config)

        try:
            manager.add_server(test_config)  # Try to add again
            print("❌ Should have raised an error")
        except MCPConfigurationError:
            print("✅ Correctly raised MCPConfigurationError for duplicate server")

        print("\n✅ All error handling tests passed!")

    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        raise
    finally:
        if os.path.exists(temp_config_file):
            os.unlink(temp_config_file)


async def main():
    """Run all tests."""
    print("🚀 Starting JSON Configuration System Tests")
    print("=" * 60)

    try:
        await test_json_config_manager()
        await test_mcp_config_integration()
        await test_error_handling()

        print("\n🎉 All tests completed successfully!")
        print("The JSON configuration system is working correctly.")

    except Exception as e:
        print(f"\n💥 Tests failed with error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
