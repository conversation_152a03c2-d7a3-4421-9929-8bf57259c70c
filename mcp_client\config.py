"""
Configuration management for the MCP client.

This module handles loading and validating configuration for MCP servers,
authentication, and client settings.
"""

import os
from typing import Any, Dict, List, Optional

from dotenv import load_dotenv

from .exceptions import MCPConfigurationError
from .json_config_manager import J<PERSON>NConfigManager
from .models import AuthConfig, ClientConfig, ServerConfig


class MCPConfig:
    """Main configuration class for the MCP client."""

    def __init__(
        self, load_env: bool = True, config_file: str = "mcp_servers.json"
    ) -> None:
        """
        Initialize MCP configuration.

        Args:
            load_env: Whether to load environment variables from .env file
            config_file: Path to the JSON configuration file
        """
        if load_env:
            load_dotenv()

        self.client = ClientConfig()
        self.auth = self._load_auth_config()
        self.json_manager = JSONConfigManager(config_file)
        self.servers = self._load_server_configs()

    def _load_auth_config(self) -> AuthConfig:
        """Load authentication configuration from environment variables."""
        return AuthConfig(
            bearer_token=os.getenv("Authorization"),
            api_key=os.getenv("API_KEY"),
            custom_headers=self._parse_custom_headers(),
        )

    def _parse_custom_headers(self) -> Dict[str, str]:
        """Parse custom headers from environment variables."""
        headers = {}

        # Look for environment variables with HEADER_ prefix
        for key, value in os.environ.items():
            if key.startswith("HEADER_"):
                header_name = key[7:].replace(
                    "_", "-"
                )  # Remove HEADER_ and convert _ to -
                headers[header_name] = value

        return headers

    def _load_server_configs(self) -> Dict[str, ServerConfig]:
        """Load server configurations from JSON file."""
        return self.json_manager.get_all_server_configs()

    def get_server_config(self, server_name: str) -> ServerConfig:
        """
        Get configuration for a specific server.

        Args:
            server_name: Name of the server

        Returns:
            Server configuration

        Raises:
            MCPConfigurationError: If server is not configured
        """
        return self.json_manager.get_server_config(server_name)

    def list_servers(self) -> List[str]:
        """
        Get list of configured server names.

        Returns:
            List of server names
        """
        return self.json_manager.list_servers()

    def add_server(
        self, server_config: ServerConfig, tools: Optional[List[str]] = None
    ) -> None:
        """
        Add a new server configuration.

        Args:
            server_config: Server configuration to add
            tools: Optional list of available tools for this server
        """
        self.json_manager.add_server(server_config, tools)
        # Refresh the local cache
        self.servers = self._load_server_configs()

    def remove_server(self, server_name: str) -> None:
        """
        Remove a server configuration.

        Args:
            server_name: Name of the server to remove

        Raises:
            MCPConfigurationError: If server is not configured
        """
        self.json_manager.remove_server(server_name)
        # Refresh the local cache
        self.servers = self._load_server_configs()

    def update_server(self, server_name: str, **kwargs: Any) -> None:
        """
        Update an existing server configuration.

        Args:
            server_name: Name of the server to update
            **kwargs: Fields to update (url, description, demo_input, timeout, max_retries, tools)

        Raises:
            MCPConfigurationError: If server doesn't exist
        """
        self.json_manager.update_server(server_name, **kwargs)
        # Refresh the local cache
        self.servers = self._load_server_configs()

    def get_server_tools(self, server_name: str) -> List[str]:
        """
        Get list of tools available for a specific server.

        Args:
            server_name: Name of the server

        Returns:
            List of tool names

        Raises:
            MCPConfigurationError: If server doesn't exist
        """
        return self.json_manager.get_server_tools(server_name)

    def get_config_metadata(self) -> Dict[str, Any]:
        """
        Get configuration file metadata.

        Returns:
            Dictionary containing metadata information
        """
        return self.json_manager.get_config_metadata()

    def export_config(self, output_file: str) -> None:
        """
        Export current configuration to a different file.

        Args:
            output_file: Path to the output file
        """
        self.json_manager.export_config(output_file)
