"""
Configuration management for the MCP client.

This module handles loading and validating configuration for MCP servers,
authentication, and client settings.
"""

import os
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse

from dotenv import load_dotenv

from .exceptions import MCPConfigurationError


@dataclass
class ServerConfig:
    """Configuration for a single MCP server."""
    
    name: str
    url: str
    description: str = ""
    demo_input: str = ""
    timeout: float = 30.0
    max_retries: int = 3
    
    def __post_init__(self) -> None:
        """Validate server configuration after initialization."""
        self._validate()
    
    def _validate(self) -> None:
        """Validate the server configuration."""
        if not self.name:
            raise MCPConfigurationError("Server name cannot be empty")
        
        if not self.url:
            raise MCPConfigurationError(f"Server URL cannot be empty for '{self.name}'")
        
        # Validate URL format
        try:
            parsed = urlparse(self.url)
            if not parsed.scheme or not parsed.netloc:
                raise MCPConfigurationError(
                    f"Invalid URL format for server '{self.name}': {self.url}"
                )
        except Exception as e:
            raise MCPConfigurationError(
                f"Invalid URL for server '{self.name}': {e}"
            ) from e
        
        if self.timeout <= 0:
            raise MCPConfigurationError(
                f"Timeout must be positive for server '{self.name}'"
            )
        
        if self.max_retries < 0:
            raise MCPConfigurationError(
                f"Max retries cannot be negative for server '{self.name}'"
            )


@dataclass
class AuthConfig:
    """Authentication configuration."""
    
    bearer_token: Optional[str] = None
    api_key: Optional[str] = None
    custom_headers: Dict[str, str] = field(default_factory=dict)
    
    def __post_init__(self) -> None:
        """Process authentication configuration after initialization."""
        self._process_bearer_token()
    
    def _process_bearer_token(self) -> None:
        """Process and validate bearer token format."""
        if self.bearer_token and not self.bearer_token.startswith("Bearer "):
            self.bearer_token = f"Bearer {self.bearer_token}"
    
    def get_auth_headers(self) -> Dict[str, str]:
        """
        Get authentication headers for HTTP requests.
        
        Returns:
            Dictionary of headers to include in requests
        """
        headers = self.custom_headers.copy()
        
        if self.bearer_token:
            headers["Authorization"] = self.bearer_token
        elif self.api_key:
            headers["X-API-Key"] = self.api_key
        
        return headers


@dataclass
class ClientConfig:
    """Client-wide configuration settings."""
    
    name: str = "minimal-mcp-client"
    version: str = "0.1.0"
    protocol_version: str = "2024-11-05"
    default_timeout: float = 30.0
    max_concurrent_connections: int = 10
    log_level: str = "INFO"
    
    def __post_init__(self) -> None:
        """Validate client configuration after initialization."""
        self._validate()
    
    def _validate(self) -> None:
        """Validate the client configuration."""
        if self.default_timeout <= 0:
            raise MCPConfigurationError("Default timeout must be positive")
        
        if self.max_concurrent_connections <= 0:
            raise MCPConfigurationError("Max concurrent connections must be positive")
        
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.log_level.upper() not in valid_log_levels:
            raise MCPConfigurationError(
                f"Invalid log level '{self.log_level}'. "
                f"Must be one of: {', '.join(valid_log_levels)}"
            )


class MCPConfig:
    """Main configuration class for the MCP client."""
    
    def __init__(self, load_env: bool = True) -> None:
        """
        Initialize MCP configuration.
        
        Args:
            load_env: Whether to load environment variables from .env file
        """
        if load_env:
            load_dotenv()
        
        self.client = ClientConfig()
        self.auth = self._load_auth_config()
        self.servers = self._load_server_configs()
    
    def _load_auth_config(self) -> AuthConfig:
        """Load authentication configuration from environment variables."""
        return AuthConfig(
            bearer_token=os.getenv("Authorization"),
            api_key=os.getenv("API_KEY"),
            custom_headers=self._parse_custom_headers()
        )
    
    def _parse_custom_headers(self) -> Dict[str, str]:
        """Parse custom headers from environment variables."""
        headers = {}
        
        # Look for environment variables with HEADER_ prefix
        for key, value in os.environ.items():
            if key.startswith("HEADER_"):
                header_name = key[7:].replace("_", "-")  # Remove HEADER_ and convert _ to -
                headers[header_name] = value
        
        return headers
    
    def _load_server_configs(self) -> Dict[str, ServerConfig]:
        """Load server configurations."""
        # Default server configurations
        default_servers = {
            "cubemcp": ServerConfig(
                name="cubemcp",
                url="https://mcp.cubeflow.cn/api/mcp/",
                description="集成多种工具和提示词的mcp server",
                demo_input="今天北京的天气"
            )
        }
        
        # TODO: Add support for loading servers from config file or environment
        return default_servers
    
    def get_server_config(self, server_name: str) -> ServerConfig:
        """
        Get configuration for a specific server.
        
        Args:
            server_name: Name of the server
            
        Returns:
            Server configuration
            
        Raises:
            MCPConfigurationError: If server is not configured
        """
        if server_name not in self.servers:
            raise MCPConfigurationError(f"Server '{server_name}' is not configured")
        
        return self.servers[server_name]
    
    def list_servers(self) -> List[str]:
        """
        Get list of configured server names.
        
        Returns:
            List of server names
        """
        return list(self.servers.keys())
    
    def add_server(self, server_config: ServerConfig) -> None:
        """
        Add a new server configuration.
        
        Args:
            server_config: Server configuration to add
        """
        self.servers[server_config.name] = server_config
    
    def remove_server(self, server_name: str) -> None:
        """
        Remove a server configuration.
        
        Args:
            server_name: Name of the server to remove
            
        Raises:
            MCPConfigurationError: If server is not configured
        """
        if server_name not in self.servers:
            raise MCPConfigurationError(f"Server '{server_name}' is not configured")
        
        del self.servers[server_name]
