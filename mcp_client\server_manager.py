"""
MCP Server Manager implementation.

This module provides high-level management of connections to multiple MCP servers,
including connection management, capability discovery, and testing functionality.
"""

import logging
from typing import Any, Dict, List

from .client import MinimalMC<PERSON>lient
from .config import MCPConfig
from .exceptions import MCPClientError

logger = logging.getLogger(__name__)


class MCPServerManager:
    """
    Manages connections to predefined MCP servers.

    This class handles connecting to the specific MCP servers listed in the
    configuration and provides a convenient interface for interacting with them.
    """

    def __init__(self, config: MCPConfig) -> None:
        """
        Initialize the server manager.
        
        Args:
            config: MCP configuration containing server and auth settings
        """
        self.config = config
        self.client = MinimalMCPClient(auth_config=config.auth)
        self.connected_servers: Dict[str, bool] = {}

    async def connect_to_all_servers(self) -> Dict[str, bool]:
        """
        Connect to all configured MCP servers.

        Returns:
            Dictionary mapping server names to connection success status
        """
        connection_results = {}

        for server_name in self.config.list_servers():
            logger.info(f"Attempting to connect to {server_name}...")
            
            try:
                server_config = self.config.get_server_config(server_name)
                success = await self.client.connect_to_server(server_config)
                connection_results[server_name] = success
                self.connected_servers[server_name] = success

                if success:
                    logger.info(f"✅ Connected to {server_name}")
                    # Discover capabilities for connected servers
                    try:
                        capabilities = await self.client.discover_capabilities(server_name)
                        logger.info(f"📋 {server_name} capabilities:")
                        logger.info(f"   Tools: {len(capabilities['tools'])}")
                        logger.info(f"   Resources: {len(capabilities['resources'])}")
                        logger.info(f"   Prompts: {len(capabilities['prompts'])}")
                    except Exception as e:
                        logger.warning(
                            f"Failed to discover capabilities for {server_name}: {e}"
                        )
                else:
                    logger.error(f"❌ Failed to connect to {server_name}")
                    
            except Exception as e:
                logger.error(f"❌ Error connecting to {server_name}: {e}")
                connection_results[server_name] = False
                self.connected_servers[server_name] = False

        return connection_results

    async def connect_to_server(self, server_name: str) -> bool:
        """
        Connect to a specific server.
        
        Args:
            server_name: Name of the server to connect to
            
        Returns:
            True if connection successful, False otherwise
        """
        try:
            server_config = self.config.get_server_config(server_name)
            success = await self.client.connect_to_server(server_config)
            self.connected_servers[server_name] = success
            
            if success:
                logger.info(f"✅ Connected to {server_name}")
                # Discover capabilities
                try:
                    await self.client.discover_capabilities(server_name)
                except Exception as e:
                    logger.warning(f"Failed to discover capabilities for {server_name}: {e}")
            else:
                logger.error(f"❌ Failed to connect to {server_name}")
                
            return success
            
        except Exception as e:
            logger.error(f"❌ Error connecting to {server_name}: {e}")
            self.connected_servers[server_name] = False
            return False

    async def disconnect_from_server(self, server_name: str) -> None:
        """
        Disconnect from a specific server.
        
        Args:
            server_name: Name of the server to disconnect from
        """
        try:
            await self.client.disconnect_server(server_name)
            self.connected_servers[server_name] = False
            logger.info(f"Disconnected from {server_name}")
        except Exception as e:
            logger.error(f"Error disconnecting from {server_name}: {e}")

    def get_connected_servers(self) -> List[str]:
        """
        Get list of currently connected servers.
        
        Returns:
            List of connected server names
        """
        return [name for name, connected in self.connected_servers.items() if connected]

    def get_connection_status(self) -> Dict[str, bool]:
        """
        Get connection status for all servers.
        
        Returns:
            Dictionary mapping server names to connection status
        """
        return self.connected_servers.copy()

    async def demonstrate_server_capabilities(self) -> None:
        """
        Demonstrate the capabilities of connected servers by listing their tools.
        """
        logger.info("🔍 Demonstrating server capabilities...")

        for server_name in self.get_connected_servers():
            try:
                server_info = self.client.get_server_info(server_name)
                server_config = self.config.get_server_config(server_name)
                
                logger.info(f"\n📊 {server_name.upper()} Server Details:")
                logger.info(f"   URL: {server_info['url']}")
                logger.info(f"   Description: {server_config.description}")

                # List tools
                if server_info["tools"]:
                    logger.info("   🔧 Available Tools:")
                    for tool in server_info["tools"]:
                        logger.info(f"      - {tool.name}: {tool.description}")

                # List resources
                if server_info["resources"]:
                    logger.info("   📄 Available Resources:")
                    for resource in server_info["resources"]:
                        logger.info(f"      - {resource.name}: {resource.description}")

                # List prompts
                if server_info["prompts"]:
                    logger.info("   💬 Available Prompts:")
                    for prompt in server_info["prompts"]:
                        logger.info(f"      - {prompt.name}: {prompt.description}")

            except Exception as e:
                logger.error(f"Error getting info for {server_name}: {e}")

    async def test_server_tools(self) -> None:
        """
        Test basic functionality of connected servers using demo inputs.
        """
        logger.info("🧪 Testing server tools with demo inputs...")

        for server_name in self.get_connected_servers():
            try:
                server_info = self.client.get_server_info(server_name)
                server_config = self.config.get_server_config(server_name)
                demo_input = server_config.demo_input

                if not demo_input:
                    logger.info(f"No demo input configured for {server_name}")
                    continue

                logger.info(f"\n🎯 Testing {server_name} with demo input:")
                logger.info(f"   Input: {demo_input}")

                # Try to call the first available tool with demo input
                if server_info["tools"]:
                    tool = server_info["tools"][0]
                    logger.info(f"   Calling tool: {tool.name}")

                    # Prepare arguments based on tool schema
                    arguments = self._prepare_tool_arguments(server_name, demo_input)

                    result = await self.client.call_tool(
                        server_name, tool.name, arguments
                    )
                    logger.info("   ✅ Tool call successful!")
                    logger.info(f"   Result preview: {str(result)[:200]}...")
                else:
                    logger.info(f"   No tools available for {server_name}")

            except Exception as e:
                logger.error(f"Error testing {server_name}: {e}")

    def _prepare_tool_arguments(self, server_name: str, demo_input: str) -> Dict[str, Any]:
        """
        Prepare arguments for tool calls based on server type and demo input.
        
        Args:
            server_name: Name of the server
            demo_input: Demo input string
            
        Returns:
            Dictionary of arguments for the tool call
        """
        # This is a simplified approach - in practice you'd parse the tool's input schema
        if server_name == "web-search":
            return {"query": demo_input}
        elif server_name == "web-crawler":
            # For crawler, we might need URL and instruction
            parts = demo_input.split(" ", 1)
            if len(parts) >= 2:
                return {"url": parts[0], "instruction": parts[1]}
            else:
                return {"url": demo_input}
        else:
            return {"input": demo_input}

    async def cleanup(self) -> None:
        """Clean up all connections."""
        logger.info("🧹 Cleaning up server manager...")
        await self.client.disconnect_all()
        self.connected_servers.clear()
        logger.info("✅ Server manager cleanup complete")
