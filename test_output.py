#!/usr/bin/env python3
"""
Test script that writes output to a file to verify the JSON configuration system.
"""

import sys
import traceback
from datetime import datetime

def main():
    output_file = "test_results.txt"
    
    try:
        with open(output_file, 'w') as f:
            f.write(f"JSON Configuration System Test Results\n")
            f.write(f"Generated at: {datetime.now()}\n")
            f.write("=" * 50 + "\n\n")
            
            # Test imports
            f.write("Testing imports...\n")
            try:
                from mcp_client import MCPConfig, JSONConfigManager, ServerConfig
                f.write("✅ All imports successful\n\n")
            except Exception as e:
                f.write(f"❌ Import failed: {e}\n")
                f.write(traceback.format_exc())
                return 1
            
            # Test JSON manager
            f.write("Testing JSONConfigManager...\n")
            try:
                manager = JSONConfigManager("mcp_servers.json")
                servers = manager.list_servers()
                f.write(f"✅ Found {len(servers)} servers: {', '.join(servers)}\n")
                
                # Test each server config
                for server_name in servers:
                    config = manager.get_server_config(server_name)
                    tools = manager.get_server_tools(server_name)
                    f.write(f"✅ {server_name}: {config.url} ({len(tools)} tools)\n")
                    
            except Exception as e:
                f.write(f"❌ JSONConfigManager test failed: {e}\n")
                f.write(traceback.format_exc())
                return 1
            
            # Test MCPConfig integration
            f.write("\nTesting MCPConfig integration...\n")
            try:
                mcp_config = MCPConfig()
                mcp_servers = mcp_config.list_servers()
                f.write(f"✅ MCPConfig loaded {len(mcp_servers)} servers: {', '.join(mcp_servers)}\n")
                
                # Verify they match
                if set(servers) == set(mcp_servers):
                    f.write("✅ JSONConfigManager and MCPConfig are in sync\n")
                else:
                    f.write("❌ JSONConfigManager and MCPConfig are out of sync\n")
                    return 1
                    
            except Exception as e:
                f.write(f"❌ MCPConfig test failed: {e}\n")
                f.write(traceback.format_exc())
                return 1
            
            # Test adding a server
            f.write("\nTesting server addition...\n")
            try:
                test_server = ServerConfig(
                    name="test_verification",
                    url="https://test.example.com/mcp",
                    description="Test server for verification",
                    demo_input="test",
                    timeout=30.0,
                    max_retries=3
                )
                
                mcp_config.add_server(test_server, tools=["test_tool"])
                
                # Verify it was added
                updated_servers = mcp_config.list_servers()
                if "test_verification" in updated_servers:
                    f.write("✅ Server addition successful\n")
                    
                    # Clean up - remove the test server
                    mcp_config.remove_server("test_verification")
                    final_servers = mcp_config.list_servers()
                    if "test_verification" not in final_servers:
                        f.write("✅ Server removal successful\n")
                    else:
                        f.write("❌ Server removal failed\n")
                        return 1
                else:
                    f.write("❌ Server addition failed\n")
                    return 1
                    
            except Exception as e:
                f.write(f"❌ Server addition/removal test failed: {e}\n")
                f.write(traceback.format_exc())
                return 1
            
            f.write("\n🎉 All verification tests passed!\n")
            f.write("The JSON configuration system is working correctly.\n")
            
        print(f"Test completed. Results written to {output_file}")
        return 0
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
