"""
Minimal MCP Client Package

This package provides a minimal implementation of a Model Context Protocol (MCP) client
that can connect to MCP servers using Streamable HTTP transport.
"""

from .client import MinimalMCPClient
from .server_manager import MCPServerManager
from .config import MCPConfig
from .exceptions import (
    MCPClientError,
    MCPConnectionError,
    MCPAuthenticationError,
    MCPServerError,
    MCPConfigurationError,
)

__version__ = "0.1.0"
__all__ = [
    "MinimalMCPClient",
    "MCPServerManager", 
    "MCPConfig",
    "MCPClientError",
    "MCPConnectionError",
    "MCPAuthenticationError",
    "MCPServerError",
    "MCPConfigurationError",
]
